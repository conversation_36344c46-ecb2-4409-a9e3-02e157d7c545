package com.xiang.proxy.server.outbound;

import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.outbound.impl.AsyncTcpDirectOutboundHandler;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.embedded.EmbeddedChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

/**
 * 异步连接使用示例
 * 展示如何使用AsyncOutboundConnection和AsyncTcpDirectOutboundHandler
 */
public class AsyncConnectionExample {
    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionExample.class);

    public static void main(String[] args) {
        try {
            // 示例1: 直接使用AsyncOutboundConnection
            demonstrateAsyncOutboundConnection();
            
            // 示例2: 使用AsyncTcpDirectOutboundHandler
            demonstrateAsyncTcpDirectOutboundHandler();
            
        } catch (Exception e) {
            logger.error("示例执行失败", e);
        }
    }

    /**
     * 示例1: 直接使用AsyncOutboundConnection
     */
    private static void demonstrateAsyncOutboundConnection() {
        logger.info("=== 示例1: AsyncOutboundConnection直接使用 ===");
        
        // 创建异步连接
        AsyncOutboundConnection connection = AsyncOutboundConnection.builder()
                .target("example.com", 80)
                .protocol("HTTP")
                .attribute("example.key", "example.value")
                .build();
        
        logger.info("创建连接: {}", connection);
        
        // 标记连接正在建立
        connection.markConnecting();
        logger.info("连接状态 - 正在连接: {}, 已连接: {}, 活跃: {}", 
                connection.isConnecting(), connection.isConnected(), connection.isActive());
        
        // 在连接建立前发送数据（会被缓存）
        ByteBuf data1 = Unpooled.copiedBuffer("Hello ".getBytes());
        ByteBuf data2 = Unpooled.copiedBuffer("World!".getBytes());
        
        CompletableFuture<Void> send1 = connection.sendData(data1);
        CompletableFuture<Void> send2 = connection.sendData(data2);
        
        logger.info("数据已缓存 - 队列大小: {}, 缓存字节数: {}", 
                connection.getPendingDataCount(), connection.getQueuedBytes());
        
        // 模拟连接建立成功
        Channel mockChannel = new EmbeddedChannel();
        connection.setBackendChannel(mockChannel);
        
        logger.info("连接建立完成 - 正在连接: {}, 已连接: {}, 队列大小: {}", 
                connection.isConnecting(), connection.isConnected(), connection.getPendingDataCount());
        
        // 关闭连接
        connection.close();
        logger.info("连接已关闭");
    }

    /**
     * 示例2: 使用AsyncTcpDirectOutboundHandler
     */
    private static void demonstrateAsyncTcpDirectOutboundHandler() {
        logger.info("=== 示例2: AsyncTcpDirectOutboundHandler使用 ===");
        
        // 创建配置
        OutboundConfig config = new OutboundConfig() {
            @Override
            public int getConnectTimeout() { return 5000; }
            
            @Override
            public int getReadTimeout() { return 30000; }
            
            @Override
            public int getWriteTimeout() { return 30000; }
            
            @Override
            public int getMaxRetries() { return 3; }
            
            @Override
            public boolean isKeepAlive() { return true; }
        };
        
        // 创建处理器
        AsyncTcpDirectOutboundHandler handler = new AsyncTcpDirectOutboundHandler("example-outbound", config);
        
        logger.info("处理器创建完成: {}", handler.getOutboundId());
        logger.info("处理器类型: {}, 优先级: {}, 可用: {}", 
                handler.getType(), handler.getPriority(), handler.isAvailable());
        
        // 创建模拟的ProxyRequest
        ProxyRequest request = createMockProxyRequest();
        
        try {
            // 建立连接（立即返回）
            CompletableFuture<OutboundConnection> connectFuture = handler.connect(request);
            
            logger.info("连接请求已提交，立即返回: {}", connectFuture.isDone());
            
            OutboundConnection connection = connectFuture.get();
            logger.info("获得连接对象: {}", connection);
            
            // 发送数据（会被缓存直到连接建立）
            ByteBuf testData = Unpooled.copiedBuffer("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n".getBytes());
            CompletableFuture<Void> sendFuture = handler.sendData(connection, testData);
            
            logger.info("数据发送请求已提交");
            
            // 获取统计信息
            logger.info("处理器统计: {}", handler.getStats());
            logger.info("活跃连接数: {}", handler.getActiveConnectionCount());
            
            // 关闭连接
            handler.closeConnection(connection);
            
        } catch (Exception e) {
            logger.error("连接处理失败", e);
        } finally {
            // 清理资源
            handler.destroy();
            logger.info("处理器已销毁");
        }
    }

    /**
     * 创建模拟的ProxyRequest
     */
    private static ProxyRequest createMockProxyRequest() {
        return ProxyRequest.builder()
                .requestId("example-request-123")
                .clientId("example-client-456")
                .sessionId(789)
                .target("example.com", 80)
                .protocol("HTTP")
                .clientChannel(new EmbeddedChannel())
                .data(null)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
