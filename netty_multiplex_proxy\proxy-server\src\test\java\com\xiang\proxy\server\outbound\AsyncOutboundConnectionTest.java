package com.xiang.proxy.server.outbound;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AsyncOutboundConnection测试类
 */
public class AsyncOutboundConnectionTest {

    private AsyncOutboundConnection connection;

    @BeforeEach
    void setUp() {
        // 创建测试连接
        connection = AsyncOutboundConnection.builder()
                .target("example.com", 80)
                .protocol("HTTP")
                .build();
    }

    @Test
    void testConnectionCreation() {
        assertNotNull(connection);
        assertEquals("example.com", connection.getTargetHost());
        assertEquals(80, connection.getTargetPort());
        assertEquals("HTTP", connection.getProtocol());
        assertNotNull(connection.getConnectionId());
        assertTrue(connection.getConnectionAge() >= 0);
    }

    @Test
    void testMarkConnecting() {
        assertFalse(connection.isConnected());
        assertFalse(connection.isConnecting());

        connection.markConnecting();

        assertTrue(connection.isConnecting());
        assertFalse(connection.isConnected());
        assertTrue(connection.isActive());
    }

    @Test
    void testSendDataWhenConnecting() throws Exception {
        connection.markConnecting();

        ByteBuf testData = Unpooled.copiedBuffer("test data".getBytes());
        CompletableFuture<Void> future = connection.sendData(testData);

        assertNotNull(future);
        assertEquals(1, connection.getPendingDataCount());
        assertTrue(connection.getQueuedBytes() > 0);
    }

    @Test
    void testConnectionFailure() {
        connection.markConnecting();

        ByteBuf testData = Unpooled.copiedBuffer("test data".getBytes());
        connection.sendData(testData);

        assertEquals(1, connection.getPendingDataCount());

        // 模拟连接失败
        RuntimeException cause = new RuntimeException("Connection failed");
        connection.setConnectionFailed(cause);

        assertFalse(connection.isConnecting());
        assertFalse(connection.isConnected());
        assertEquals(0, connection.getPendingDataCount()); // 数据应该被清理
    }

    @Test
    void testQueueSizeLimit() {
        connection.markConnecting();

        // 尝试添加超过限制的数据
        for (int i = 0; i < 1001; i++) {
            ByteBuf testData = Unpooled.copiedBuffer(("test data " + i).getBytes());
            CompletableFuture<Void> future = connection.sendData(testData);

            if (i >= 1000) {
                // 超过限制的数据应该失败
                assertTrue(future.isCompletedExceptionally());
            }
        }

        // 队列大小不应超过限制
        assertTrue(connection.getPendingDataCount() <= 1000);
    }

    @Test
    void testAttributes() {
        connection.setAttribute("test.key", "test.value");
        
        assertTrue(connection.hasAttribute("test.key"));
        assertEquals("test.value", connection.getAttribute("test.key"));
        assertEquals("default", connection.getAttribute("nonexistent.key", "default"));
        assertFalse(connection.hasAttribute("nonexistent.key"));
    }

    @Test
    void testToString() {
        String str = connection.toString();
        
        assertNotNull(str);
        assertTrue(str.contains("AsyncOutboundConnection"));
        assertTrue(str.contains("example.com"));
        assertTrue(str.contains("80"));
        assertTrue(str.contains("HTTP"));
    }

    @Test
    void testBuilderValidation() {
        // 测试必需字段验证
        assertThrows(IllegalArgumentException.class, () -> {
            AsyncOutboundConnection.builder()
                    .target("", 80)
                    .protocol("HTTP")
                    .build();
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            AsyncOutboundConnection.builder()
                    .target("example.com", 0)
                    .protocol("HTTP")
                    .build();
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            AsyncOutboundConnection.builder()
                    .target("example.com", 80)
                    .protocol("")
                    .build();
        });
    }
}
