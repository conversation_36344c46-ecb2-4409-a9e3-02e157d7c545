package com.xiang.proxy.server.pool;

import com.xiang.proxy.server.config.ConnectionPoolConfig;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 增强的连接池实现 - 结合server DispatchProcessor的ActiveConnection机制
 * 支持连接复用、消息队列缓存、智能清理等功能
 */
public class EnhancedConnectionPool {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedConnectionPool.class);
    
    /**
     * 连接池映射 - 按目标主机分组
     */
    private final ConcurrentHashMap<String, ConnectionGroup> connectionGroups;
    
    /**
     * 统计信息
     */
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong queuedMessages = new AtomicLong(0);
    
    /**
     * 清理定时器
     */
    private final ScheduledExecutorService cleanupExecutor;
    
    public EnhancedConnectionPool() {
        this.connectionGroups = new ConcurrentHashMap<>();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "enhanced-connection-pool-cleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 启动定时清理任务
        cleanupExecutor.scheduleAtFixedRate(this::performCleanup, 30, 30, TimeUnit.SECONDS);
        logger.info("增强连接池已初始化");
    }
    
    /**
     * 获取或创建连接组
     */
    public ConnectionGroup getOrCreateConnectionGroup(String hostKey) {
        return connectionGroups.computeIfAbsent(hostKey, key -> {
            logger.debug("创建新的连接组: {}", key);
            return new ConnectionGroup(key);
        });
    }
    
    /**
     * 获取连接 - 支持连接复用和消息队列
     */
    public EnhancedConnection getConnection(String hostKey, String clientId) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            missCount.incrementAndGet();
            return null;
        }
        
        ConnectionGroup group = connectionGroups.get(hostKey);
        if (group == null) {
            missCount.incrementAndGet();
            return null;
        }
        
        EnhancedConnection connection = group.getConnection(clientId);
        if (connection != null) {
            hitCount.incrementAndGet();
            activeConnections.incrementAndGet();
            logger.debug("连接池命中: {}, 客户端: {}", hostKey, clientId);
        } else {
            missCount.incrementAndGet();
            logger.debug("连接池未命中: {}, 客户端: {}", hostKey, clientId);
        }
        
        return connection;
    }
    
    /**
     * 归还连接
     */
    public void returnConnection(String hostKey, String clientId, EnhancedConnection connection) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            connection.close();
            return;
        }
        
        ConnectionGroup group = connectionGroups.get(hostKey);
        if (group != null) {
            if (group.returnConnection(clientId, connection)) {
                activeConnections.decrementAndGet();
                logger.debug("连接已归还: {}, 客户端: {}", hostKey, clientId);
            } else {
                connection.close();
                logger.debug("连接归还失败，已关闭: {}, 客户端: {}", hostKey, clientId);
            }
        } else {
            connection.close();
        }
    }
    
    /**
     * 创建新连接
     */
    public EnhancedConnection createConnection(String hostKey, String clientId, Channel channel) {
        totalConnections.incrementAndGet();
        return new EnhancedConnection(hostKey, clientId, channel);
    }
    
    /**
     * 执行清理任务
     */
    private void performCleanup() {
        try {
            int totalCleaned = 0;
            for (ConnectionGroup group : connectionGroups.values()) {
                totalCleaned += group.cleanup();
            }
            
            // 清理空的连接组
            connectionGroups.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            
            if (totalCleaned > 0) {
                logger.info("连接池清理完成，清理了 {} 个连接", totalCleaned);
            }
        } catch (Exception e) {
            logger.error("连接池清理时发生异常", e);
        }
    }
    
    /**
     * 获取统计信息
     */
    public PoolStats getStats() {
        long pooledConnections = connectionGroups.values().stream()
            .mapToLong(ConnectionGroup::size)
            .sum();
            
        return new PoolStats(
            totalConnections.get(),
            pooledConnections,
            activeConnections.get(),
            hitCount.get(),
            missCount.get(),
            totalMessages.get(),
            queuedMessages.get()
        );
    }
    
    /**
     * 关闭连接池
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            cleanupExecutor.shutdownNow();
        }
        
        // 关闭所有连接
        connectionGroups.values().forEach(ConnectionGroup::shutdown);
        connectionGroups.clear();
        
        logger.info("增强连接池已关闭");
    }
    
    /**
     * 连接组 - 管理同一目标主机的连接
     */
    public class ConnectionGroup {
        private final String hostKey;
        private final ConcurrentHashMap<String, EnhancedConnection> connections;
        
        public ConnectionGroup(String hostKey) {
            this.hostKey = hostKey;
            this.connections = new ConcurrentHashMap<>();
        }
        
        public EnhancedConnection getConnection(String clientId) {
            return connections.get(clientId);
        }
        
        public boolean returnConnection(String clientId, EnhancedConnection connection) {
            if (connection.isValid() && connections.size() < ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST) {
                connections.put(clientId, connection);
                return true;
            }
            return false;
        }
        
        public int cleanup() {
            int cleaned = 0;
            connections.entrySet().removeIf(entry -> {
                EnhancedConnection conn = entry.getValue();
                if (!conn.isValid() || conn.isTimeout()) {
                    conn.close();
                    return true;
                }
                return false;
            });
            return cleaned;
        }
        
        public long size() {
            return connections.size();
        }
        
        public boolean isEmpty() {
            return connections.isEmpty();
        }
        
        public void shutdown() {
            connections.values().forEach(EnhancedConnection::close);
            connections.clear();
        }
    }
    
    /**
     * 增强连接 - 结合ActiveConnection的功能
     */
    public class EnhancedConnection {
        private final String hostKey;
        private final String clientId;
        private final Channel channel;
        private final ConcurrentLinkedQueue<ByteBuf> messageQueue;
        private final long createTime;
        private volatile long lastActiveTime;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        
        public EnhancedConnection(String hostKey, String clientId, Channel channel) {
            this.hostKey = hostKey;
            this.clientId = clientId;
            this.channel = channel;
            this.messageQueue = new ConcurrentLinkedQueue<>();
            this.createTime = System.currentTimeMillis();
            this.lastActiveTime = createTime;
        }
        
        /**
         * 发送消息 - 支持队列缓存
         */
        public boolean sendMessage(ByteBuf message) {
            totalMessages.incrementAndGet();
            messageCount.incrementAndGet();
            
            if (channel.isActive()) {
                // 先发送队列中的消息
                flushQueuedMessages();
                // 然后发送当前消息
                channel.writeAndFlush(message);
                updateLastActiveTime();
                return true;
            } else {
                // 连接不活跃，加入队列
                if (messageQueue.size() < 1000) { // 限制队列大小
                    messageQueue.offer(message);
                    queuedMessages.incrementAndGet();
                    return true;
                } else {
                    ReferenceCountUtil.release(message);
                    return false;
                }
            }
        }
        
        /**
         * 刷新队列中的消息
         */
        public void flushQueuedMessages() {
            if (messageQueue.isEmpty() || !channel.isActive()) {
                return;
            }
            
            ByteBuf message;
            while ((message = messageQueue.poll()) != null) {
                channel.write(message);
                queuedMessages.decrementAndGet();
            }
            channel.flush();
            updateLastActiveTime();
        }
        
        /**
         * 检查连接是否有效
         */
        public boolean isValid() {
            return channel != null && channel.isActive() && channel.isWritable();
        }
        
        /**
         * 检查是否超时
         */
        public boolean isTimeout() {
            long currentTime = System.currentTimeMillis();
            return (currentTime - lastActiveTime) > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT;
        }
        
        /**
         * 更新最后活跃时间
         */
        public void updateLastActiveTime() {
            this.lastActiveTime = System.currentTimeMillis();
        }
        
        /**
         * 关闭连接
         */
        public void close() {
            // 释放队列中的消息
            ByteBuf message;
            while ((message = messageQueue.poll()) != null) {
                ReferenceCountUtil.release(message);
                queuedMessages.decrementAndGet();
            }
            
            // 关闭Channel
            if (channel != null && channel.isActive()) {
                channel.close();
            }
        }
        
        // Getters
        public String getHostKey() { return hostKey; }
        public String getClientId() { return clientId; }
        public Channel getChannel() { return channel; }
        public int getQueueSize() { return messageQueue.size(); }
        public long getLastActiveTime() { return lastActiveTime; }
        public int getMessageCount() { return messageCount.get(); }
        public long getIdleTime() { return System.currentTimeMillis() - lastActiveTime; }
    }
    
    /**
     * 连接池统计信息
     */
    public static class PoolStats {
        public final long totalConnections;
        public final long pooledConnections;
        public final long activeConnections;
        public final long hitCount;
        public final long missCount;
        public final long totalMessages;
        public final long queuedMessages;
        
        public PoolStats(long totalConnections, long pooledConnections, long activeConnections,
                        long hitCount, long missCount, long totalMessages, long queuedMessages) {
            this.totalConnections = totalConnections;
            this.pooledConnections = pooledConnections;
            this.activeConnections = activeConnections;
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.totalMessages = totalMessages;
            this.queuedMessages = queuedMessages;
        }
        
        public double getHitRate() {
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total * 100.0 : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("EnhancedPoolStats{total=%d, pooled=%d, active=%d, hitRate=%.2f%%, " +
                    "hits=%d, misses=%d, totalMsg=%d, queuedMsg=%d}",
                    totalConnections, pooledConnections, activeConnections, getHitRate(),
                    hitCount, missCount, totalMessages, queuedMessages);
        }
    }
}
