package com.xiang.proxy.server.outbound.impl;

import com.xiang.proxy.server.outbound.OutboundConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AsyncTcpDirectOutboundHandler测试类
 */
public class AsyncTcpDirectOutboundHandlerTest {

    private AsyncTcpDirectOutboundHandler handler;
    private OutboundConfig config;

    @BeforeEach
    void setUp() {
        // 创建简单的配置对象
        config = new OutboundConfig() {
            @Override
            public int getConnectTimeout() {
                return 5000;
            }

            @Override
            public int getReadTimeout() {
                return 30000;
            }

            @Override
            public int getWriteTimeout() {
                return 30000;
            }

            @Override
            public int getMaxRetries() {
                return 3;
            }

            @Override
            public boolean isKeepAlive() {
                return true;
            }
        };

        // 创建处理器
        handler = new AsyncTcpDirectOutboundHandler("test-outbound", config);
    }

    @Test
    void testHandlerCreation() {
        assertNotNull(handler);
        assertEquals("test-outbound", handler.getOutboundId());
        assertEquals(config, handler.getConfig());
        assertTrue(handler.isAvailable());
        assertEquals("AsyncTcpDirect", handler.getType());
        assertEquals(10, handler.getPriority());
    }

    @Test
    void testGetStats() {
        String stats = handler.getStats();

        assertNotNull(stats);
        assertTrue(stats.contains("AsyncTcpDirectOutbound"));
        assertTrue(stats.contains("test-outbound"));
        assertTrue(stats.contains("total="));
        assertTrue(stats.contains("success="));
        assertTrue(stats.contains("failure="));
        assertTrue(stats.contains("active="));
        assertTrue(stats.contains("successRate="));
    }

    @Test
    void testActiveConnectionCount() {
        assertEquals(0, handler.getActiveConnectionCount());
    }

    @Test
    void testCleanupInactiveConnections() {
        // 测试清理方法不会抛出异常
        assertDoesNotThrow(() -> handler.cleanupInactiveConnections());
    }

    @Test
    void testCloseAllConnections() {
        // 测试关闭所有连接方法不会抛出异常
        assertDoesNotThrow(() -> handler.closeAllConnections());
        assertEquals(0, handler.getActiveConnectionCount());
    }

    @Test
    void testDestroy() {
        // 测试销毁方法不会抛出异常
        assertDoesNotThrow(() -> handler.destroy());
        assertEquals(0, handler.getActiveConnectionCount());
    }
}
